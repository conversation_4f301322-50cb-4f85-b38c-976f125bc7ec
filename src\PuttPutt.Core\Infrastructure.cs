﻿using Microsoft.Extensions.DependencyInjection;
using PuttPutt.Core.Implementations;
using PuttPutt.Core.Interfaces;

namespace PuttPutt.Core;

internal class Infrastructure
{

    public static void AddInfrastructureServices(IServiceCollection services)
    {
        // Register any infrastructure services here
        // For example, database context, logging, etc.
        // services.AddDbContext<MyDbContext>(options => ...);
        // services.AddLogging();

        // Example: Registering a player management service
        services.AddScoped<IPlayerManagement, PlayerManagement>();

        // Add other infrastructure-related services as needed
    }

}
