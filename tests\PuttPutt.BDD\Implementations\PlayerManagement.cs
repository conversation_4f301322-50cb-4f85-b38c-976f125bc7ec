﻿using PuttPutt.Core.Interfaces;
using PuttPutt.Core.Models;

namespace PuttPutt.BDD.Implementations;

internal class PlayerManagement : IPlayerManagement
{
    // in-memory storage for players
    private readonly List<Player> _players = [];

    public PlayerManagement()
    {
    }

    public Task<Result<Player>> AddPlayerAsync(string name, string color, bool isFavorite = false)
    {
        var validation = ValidatePlayerNameInternal(name);

        if (!validation.IsSuccess)
            return Task.FromResult(Result.Failure<Player>(validation.ErrorMessage, validation.ErrorCode));

        if (string.IsNullOrWhiteSpace(color))
            return Task.FromResult(Result.Failure<Player>("Player color is required.", PlayerManagementErrors.ColorRequired));

        if (_players.Any(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase)))
            return Task.FromResult(Result.Failure<Player>("A player with this name already exists.", PlayerManagementErrors.DuplicatePlayerName));

        var player = new Player(name, color, isFavorite);
        _players.Add(player);

        return Task.FromResult(Result.Success(player));
    }

    public Task<Result<IReadOnlyList<Player>>> GetAllPlayersAsync()
    {
        var ordered = _players.OrderByDescending(p => p.IsFavorite).ThenBy(p => p.Name).ToList();

        return Task.FromResult(Result.Success<IReadOnlyList<Player>>(ordered));
    }

    public Task<Result<IReadOnlyList<Player>>> GetFavoritePlayersAsync()
    {
        var favorites = _players.Where(p => p.IsFavorite).OrderBy(p => p.Name).ToList();

        return Task.FromResult(Result.Success<IReadOnlyList<Player>>(favorites));
    }

    public Task<Result<Player>> GetPlayerByNameAsync(string name)
    {
        var player = _players.FirstOrDefault(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase));

        if (player == null)
            return Task.FromResult(Result.Failure<Player>("Player not found.", PlayerManagementErrors.PlayerNotFound));

        return Task.FromResult(Result.Success(player));
    }

    public Task<bool> PlayerExistsAsync(string name)
    {
        return Task.FromResult(_players.Any(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase)));
    }

    public Task<Result> RemovePlayerAsync(string name)
    {
        var player = _players.FirstOrDefault(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase));

        if (player == null)
            return Task.FromResult(Result.Failure("Player not found.", PlayerManagementErrors.PlayerNotFound));

        _players.Remove(player);

        return Task.FromResult(Result.Success());
    }

    public Task<Result<Player>> ToggleFavoriteAsync(string name)
    {
        var player = _players.FirstOrDefault(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase));

        if (player == null)
            return Task.FromResult(Result.Failure<Player>("Player not found.", PlayerManagementErrors.PlayerNotFound));

        player.IsFavorite = !player.IsFavorite;

        return Task.FromResult(Result.Success(player));
    }

    public Task<Result<Player>> UpdatePlayerAsync(string originalName, string newName, string color, bool isFavorite)
    {
        var player = _players.FirstOrDefault(p => p.Name.Equals(originalName, StringComparison.OrdinalIgnoreCase));

        if (player == null)
            return Task.FromResult(Result.Failure<Player>("Player not found.", PlayerManagementErrors.PlayerNotFound));

        if (!originalName.Equals(newName, StringComparison.OrdinalIgnoreCase) && _players.Any(p => p.Name.Equals(newName, StringComparison.OrdinalIgnoreCase)))
            return Task.FromResult(Result.Failure<Player>("A player with this name already exists.", PlayerManagementErrors.DuplicatePlayerName));

        var validation = ValidatePlayerNameInternal(newName);

        if (!validation.IsSuccess)
            return Task.FromResult(Result.Failure<Player>(validation.ErrorMessage, validation.ErrorCode));

        if (string.IsNullOrWhiteSpace(color))
            return Task.FromResult(Result.Failure<Player>("Player color is required.", PlayerManagementErrors.ColorRequired));

        player.Name = newName;
        player.Color = color;
        player.IsFavorite = isFavorite;

        return Task.FromResult(Result.Success(player));
    }

    public Task<Result> ValidatePlayerNameAsync(string name, string? excludeExistingName = null)
    {
        var validation = ValidatePlayerNameInternal(name);

        if (!validation.IsSuccess)
            return Task.FromResult(validation);

        if (_players.Any(p => p.Name.Equals(name, StringComparison.OrdinalIgnoreCase) && !p.Name.Equals(excludeExistingName, StringComparison.OrdinalIgnoreCase)))
            return Task.FromResult(Result.Failure("A player with this name already exists.", PlayerManagementErrors.DuplicatePlayerName));

        return Task.FromResult(Result.Success());
    }

    private Result ValidatePlayerNameInternal(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return Result.Failure("Player name is required.", PlayerManagementErrors.PlayerNameRequired);

        if (name.Length > 50)
            return Result.Failure("Player name cannot exceed 50 characters.", PlayerManagementErrors.PlayerNameTooLong);

        return Result.Success();
    }
}
