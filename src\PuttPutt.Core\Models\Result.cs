namespace PuttPutt.Core.Models;

/// <summary>
/// Represents the result of an operation that can succeed or fail.
/// Provides a clear way to handle success/failure scenarios with descriptive error information.
/// </summary>
public class Result
{
    /// <summary>
    /// Gets a value indicating whether the operation was successful.
    /// </summary>
    public bool IsSuccess { get; protected set; }

    /// <summary>
    /// Gets a value indicating whether the operation failed.
    /// </summary>
    public bool IsFailure => !IsSuccess;

    /// <summary>
    /// Gets the error message if the operation failed.
    /// </summary>
    public string ErrorMessage { get; protected set; } = string.Empty;

    /// <summary>
    /// Gets the error code if the operation failed.
    /// </summary>
    public string ErrorCode { get; protected set; } = string.Empty;

    /// <summary>
    /// Initializes a new instance of the <see cref="Result"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the operation was successful.</param>
    /// <param name="errorMessage">The error message if the operation failed.</param>
    /// <param name="errorCode">The error code if the operation failed.</param>
    protected Result(bool isSuccess, string errorMessage = "", string errorCode = "")
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;
    }

    /// <summary>
    /// Creates a successful result.
    /// </summary>
    /// <returns>A successful Result instance.</returns>
    public static Result Success()
    {
        return new Result(true);
    }

    /// <summary>
    /// Creates a failed result with an error message.
    /// </summary>
    /// <param name="errorMessage">The error message describing why the operation failed.</param>
    /// <param name="errorCode">Optional error code for categorizing the error.</param>
    /// <returns>A failed Result instance.</returns>
    public static Result Failure(string errorMessage, string errorCode = "")
    {
        return new Result(false, errorMessage, errorCode);
    }

    /// <summary>
    /// Creates a successful result with a value.
    /// </summary>
    /// <typeparam name="T">The type of the value.</typeparam>
    /// <param name="value">The value to include in the result.</param>
    /// <returns>A successful Result instance containing the value.</returns>
    public static Result<T> Success<T>(T value)
    {
        return new Result<T>(true, value);
    }

    /// <summary>
    /// Creates a failed result with an error message.
    /// </summary>
    /// <typeparam name="T">The type of the value.</typeparam>
    /// <param name="errorMessage">The error message describing why the operation failed.</param>
    /// <param name="errorCode">Optional error code for categorizing the error.</param>
    /// <returns>A failed Result instance.</returns>
    public static Result<T> Failure<T>(string errorMessage, string errorCode = "")
    {
        return new Result<T>(false, default, errorMessage, errorCode);
    }
}

/// <summary>
/// Represents the result of an operation that can succeed or fail and returns a value on success.
/// </summary>
/// <typeparam name="T">The type of the value returned on success.</typeparam>
public class Result<T> : Result
{
    /// <summary>
    /// Gets the value if the operation was successful.
    /// </summary>
    public T? Value { get; private set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="Result{T}"/> class.
    /// </summary>
    /// <param name="isSuccess">Whether the operation was successful.</param>
    /// <param name="value">The value if the operation was successful.</param>
    /// <param name="errorMessage">The error message if the operation failed.</param>
    /// <param name="errorCode">The error code if the operation failed.</param>
    internal Result(bool isSuccess, T? value, string errorMessage = "", string errorCode = "")
        : base(isSuccess, errorMessage, errorCode)
    {
        Value = value;
    }

    /// <summary>
    /// Implicitly converts a value to a successful Result.
    /// </summary>
    /// <param name="value">The value to convert.</param>
    /// <returns>A successful Result containing the value.</returns>
    public static implicit operator Result<T>(T value)
    {
        return Success(value);
    }
}

/// <summary>
/// Provides common error codes for player management operations.
/// </summary>
public static class PlayerManagementErrors
{
    public const string PlayerNotFound = "PLAYER_NOT_FOUND";
    public const string DuplicatePlayerName = "DUPLICATE_PLAYER_NAME";
    public const string InvalidPlayerName = "INVALID_PLAYER_NAME";
    public const string PlayerNameRequired = "PLAYER_NAME_REQUIRED";
    public const string PlayerNameTooLong = "PLAYER_NAME_TOO_LONG";
    public const string InvalidColor = "INVALID_COLOR";
    public const string ColorRequired = "COLOR_REQUIRED";
}
