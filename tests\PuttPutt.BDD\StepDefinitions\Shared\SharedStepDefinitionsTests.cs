using Reqnroll;
using Reqnroll.Assist;
using Xunit;
using PuttPutt.Core.Models;

namespace PuttPutt.BDD.StepDefinitions.Shared;

/// <summary>
/// Unit tests for SharedStepDefinitions to demonstrate and validate the implementation
/// Note: These tests focus on testing the core logic and data models
/// </summary>
public class SharedStepDefinitionsTests
{
    [Fact]
    public void Player_Constructor_ShouldSetPropertiesCorrectly()
    {
        // Arrange & Act
        var player = new Player("John Doe", "Blue", true);

        // Assert
        Assert.Equal("<PERSON>", player.Name);
        Assert.Equal("Blue", player.Color);
        Assert.True(player.IsFavorite);
    }

    [Fact]
    public void Player_DefaultConstructor_ShouldInitializeWithEmptyValues()
    {
        // Arrange & Act
        var player = new Player();

        // Assert
        Assert.Equal(string.Empty, player.Name);
        Assert.Equal(string.Empty, player.Color);
        Assert.False(player.IsFavorite);
    }

    [Fact]
    public void Player_ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var player = new Player("<PERSON>", "Blue", true);

        // Act
        var result = player.ToString();

        // Assert
        Assert.Equal("John Doe (Blue) - Favorite: True", result);
    }

    [Fact]
    public void Player_ToString_WithFalse_ShouldReturnFormattedString()
    {
        // Arrange
        var player = new Player("Jane Smith", "Red", false);

        // Act
        var result = player.ToString();

        // Assert
        Assert.Equal("Jane Smith (Red) - Favorite: False", result);
    }

    [Fact]
    public void ParseBooleanValue_WithValidYesNo_ShouldReturnCorrectValues()
    {
        // Test the boolean parsing logic through reflection since it's private
        var type = typeof(SharedStepDefinitions);
        var method = type.GetMethod("ParseBooleanValue",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        Assert.NotNull(method);

        // Test Yes/No
        Assert.True((bool)method.Invoke(null, new object[] { "Yes", "test field" })!);
        Assert.False((bool)method.Invoke(null, new object[] { "No", "test field" })!);

        // Test True/False
        Assert.True((bool)method.Invoke(null, new object[] { "True", "test field" })!);
        Assert.False((bool)method.Invoke(null, new object[] { "False", "test field" })!);

        // Test 1/0
        Assert.True((bool)method.Invoke(null, new object[] { "1", "test field" })!);
        Assert.False((bool)method.Invoke(null, new object[] { "0", "test field" })!);

        // Test case insensitive
        Assert.True((bool)method.Invoke(null, new object[] { "yes", "test field" })!);
        Assert.True((bool)method.Invoke(null, new object[] { "YES", "test field" })!);
    }

    [Fact]
    public void ParseBooleanValue_WithInvalidValue_ShouldThrowException()
    {
        // Test the boolean parsing logic through reflection since it's private
        var type = typeof(SharedStepDefinitions);
        var method = type.GetMethod("ParseBooleanValue",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        Assert.NotNull(method);

        // Test invalid value
        var exception = Assert.Throws<System.Reflection.TargetInvocationException>(() =>
            method.Invoke(null, new object[] { "Maybe", "test field" }));

        Assert.IsType<ArgumentException>(exception.InnerException);
        Assert.Contains("Invalid boolean value 'Maybe'", exception.InnerException!.Message);
    }

    [Fact]
    public void ParseBooleanValue_WithEmptyValue_ShouldThrowException()
    {
        // Test the boolean parsing logic through reflection since it's private
        var type = typeof(SharedStepDefinitions);
        var method = type.GetMethod("ParseBooleanValue",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        Assert.NotNull(method);

        // Test empty value
        var exception = Assert.Throws<System.Reflection.TargetInvocationException>(() =>
            method.Invoke(null, new object[] { "", "test field" }));

        Assert.IsType<ArgumentException>(exception.InnerException);
        Assert.Contains("test field cannot be empty", exception.InnerException!.Message);
    }

    [Fact]
    public void Table_Creation_ShouldWorkCorrectly()
    {
        // Arrange & Act
        var table = new Table("Name", "Color", "Favorite");
        table.AddRow("John Doe", "Blue", "Yes");
        table.AddRow("Jane Smith", "Red", "No");

        // Assert
        Assert.Equal(3, table.Header.Count);
        Assert.Contains("Name", table.Header);
        Assert.Contains("Color", table.Header);
        Assert.Contains("Favorite", table.Header);
        Assert.Equal(2, table.Rows.Count);
        Assert.Equal("John Doe", table.Rows[0]["Name"]);
        Assert.Equal("Blue", table.Rows[0]["Color"]);
        Assert.Equal("Yes", table.Rows[0]["Favorite"]);
    }

    [Fact]
    public void PlayerTableRow_Conversion_ShouldWorkCorrectly()
    {
        // Arrange
        var table = new Table("Name", "Color", "Favorite");
        table.AddRow("John Doe", "Blue", "Yes");
        table.AddRow("Jane Smith", "Red", "No");

        // Act
        var playerRows = table.CreateSet<PlayerTableRow>().ToList();

        // Assert
        Assert.Equal(2, playerRows.Count);

        var firstPlayer = playerRows[0];
        Assert.Equal("John Doe", firstPlayer.Name);
        Assert.Equal("Blue", firstPlayer.Color);
        Assert.Equal("Yes", firstPlayer.Favorite);

        var secondPlayer = playerRows[1];
        Assert.Equal("Jane Smith", secondPlayer.Name);
        Assert.Equal("Red", secondPlayer.Color);
        Assert.Equal("No", secondPlayer.Favorite);
    }

    [Fact]
    public void Player_Properties_CanBeSetAndGet()
    {
        // Arrange
        var player = new Player();

        // Act
        player.Name = "Test Player";
        player.Color = "Purple";
        player.IsFavorite = true;

        // Assert
        Assert.Equal("Test Player", player.Name);
        Assert.Equal("Purple", player.Color);
        Assert.True(player.IsFavorite);
    }
}
