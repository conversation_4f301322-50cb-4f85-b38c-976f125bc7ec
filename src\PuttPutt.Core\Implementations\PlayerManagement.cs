﻿using PuttPutt.Core.Interfaces;
using PuttPutt.Core.Models;

namespace PuttPutt.Core.Implementations;

internal class PlayerManagement : IPlayerManagement
{
    public PlayerManagement()
    {

    }

    public Task<Result<Player>> AddPlayerAsync(string name, string color, bool isFavorite = false)
    {
        throw new NotImplementedException();
    }

    public Task<Result<IReadOnlyList<Player>>> GetAllPlayersAsync()
    {
        throw new NotImplementedException();
    }

    public Task<Result<IReadOnlyList<Player>>> GetFavoritePlayersAsync()
    {
        throw new NotImplementedException();
    }

    public Task<Result<Player>> GetPlayerByNameAsync(string name)
    {
        throw new NotImplementedException();
    }

    public Task<bool> PlayerExistsAsync(string name)
    {
        throw new NotImplementedException();
    }

    public Task<Result> RemovePlayerAsync(string name)
    {
        throw new NotImplementedException();
    }

    public Task<Result<Player>> ToggleFavoriteAsync(string name)
    {
        throw new NotImplementedException();
    }

    public Task<Result<Player>> UpdatePlayerAsync(string originalName, string newName, string color, bool isFavorite)
    {
        throw new NotImplementedException();
    }

    public Task<Result> ValidatePlayerNameAsync(string name, string? excludeExistingName = null)
    {
        throw new NotImplementedException();
    }
}
