﻿using PuttPutt.Core.Models;

namespace PuttPutt.Core.Interfaces;

/// <summary>
/// Interface for managing player profiles in the Putt-Putt application.
/// Provides comprehensive player management functionality with Result pattern for clear success/failure handling.
/// </summary>
public interface IPlayerManagement
{
    /// <summary>
    /// Retrieves all players from the system, ordered with favorites first.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result with the list of players.</returns>
    Task<Result<IReadOnlyList<Player>>> GetAllPlayersAsync();

    /// <summary>
    /// Retrieves a specific player by name.
    /// </summary>
    /// <param name="name">The name of the player to retrieve.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result with the player if found.</returns>
    Task<Result<Player>> GetPlayerByNameAsync(string name);

    /// <summary>
    /// Adds a new player to the system.
    /// Validates that the name is not empty, does not exceed maximum length (50 characters),
    /// and is not a duplicate of an existing player.
    /// </summary>
    /// <param name="name">The player's name (required, max 50 characters).</param>
    /// <param name="color">The player's color identifier (required).</param>
    /// <param name="isFavorite">Whether the player should be marked as a favorite.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result with the created player.</returns>
    Task<Result<Player>> AddPlayerAsync(string name, string color, bool isFavorite = false);

    /// <summary>
    /// Updates an existing player's information.
    /// Validates that the new name (if changed) meets the same requirements as adding a new player.
    /// </summary>
    /// <param name="originalName">The current name of the player to update.</param>
    /// <param name="newName">The new name for the player (required, max 50 characters).</param>
    /// <param name="color">The new color for the player (required).</param>
    /// <param name="isFavorite">Whether the player should be marked as a favorite.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result with the updated player.</returns>
    Task<Result<Player>> UpdatePlayerAsync(string originalName, string newName, string color, bool isFavorite);

    /// <summary>
    /// Removes a player from the system.
    /// </summary>
    /// <param name="name">The name of the player to remove.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result indicating success or failure.</returns>
    Task<Result> RemovePlayerAsync(string name);

    /// <summary>
    /// Toggles the favorite status of a player.
    /// </summary>
    /// <param name="name">The name of the player whose favorite status should be toggled.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result with the updated player.</returns>
    Task<Result<Player>> ToggleFavoriteAsync(string name);

    /// <summary>
    /// Retrieves only the players marked as favorites, ordered by name.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result with the list of favorite players.</returns>
    Task<Result<IReadOnlyList<Player>>> GetFavoritePlayersAsync();

    /// <summary>
    /// Validates a player name according to business rules.
    /// </summary>
    /// <param name="name">The name to validate.</param>
    /// <param name="excludeExistingName">Optional existing name to exclude from duplicate checking (used for updates).</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a Result indicating if the name is valid.</returns>
    Task<Result> ValidatePlayerNameAsync(string name, string? excludeExistingName = null);

    /// <summary>
    /// Checks if a player with the specified name exists.
    /// </summary>
    /// <param name="name">The name to check.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains true if the player exists, false otherwise.</returns>
    Task<bool> PlayerExistsAsync(string name);
}
