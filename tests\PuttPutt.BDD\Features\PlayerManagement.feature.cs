﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:2.0.0.0
//      Reqnroll Generator Version:2.0.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace PuttPutt.BDD.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [Xunit.TraitAttribute("Category", "player-management")]
    public partial class PlayerManagementFeature : object, Xunit.IClassFixture<PlayerManagementFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "player-management"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Player Management", "  As a putt-putt player\r\n  I want to manage player profiles\r\n  So that I can easi" +
                "ly set up games with regular players", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "PlayerManagement.feature"
#line hidden
        
        public PlayerManagementFeature(PlayerManagementFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
  #line hidden
#line 8
    await testRunner.GivenAsync("the app is launched", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            try
            {
                await this.TestInitializeAsync();
            }
            catch (System.Exception e1)
            {
                try
                {
                    ((Xunit.IAsyncLifetime)(this)).DisposeAsync();
                }
                catch (System.Exception e2)
                {
                    throw new System.AggregateException("Test initialization failed", e1, e2);
                }
                throw;
            }
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Navigate to player management screen")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Navigate to player management screen")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task NavigateToPlayerManagementScreen()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Navigate to player management screen", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 12
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 13
    await testRunner.WhenAsync("I tap on the \"Manage Players\" option", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 14
    await testRunner.ThenAsync("I should see the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 15
    await testRunner.AndAsync("I should see a list of existing players", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableTheoryAttribute(DisplayName="Add a new player profile")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Add a new player profile")]
        [Xunit.TraitAttribute("Category", "smoke")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        [Xunit.InlineDataAttribute("no existing players", "John Doe", "blue", "1", new string[0])]
        [Xunit.InlineDataAttribute("the following players: John Doe (Blue, Favorite), Jane Smith (Red), Bob Johnson (" +
            "Green), Alice Brown (Yellow, Favorite)", "Michael Jordan", "purple", "5", new string[0])]
        public async global::System.Threading.Tasks.Task AddANewPlayerProfile(string existing_Players, string player_Name, string player_Color, string expected_Position, string[] exampleTags)
        {
            string[] @__tags = new string[] {
                    "smoke",
                    "happy-path"};
            if ((exampleTags != null))
            {
                @__tags = System.Linq.Enumerable.ToArray(System.Linq.Enumerable.Concat(@__tags, exampleTags));
            }
            string[] tagsOfScenario = @__tags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("existing_players", existing_Players);
            argumentsOfScenario.Add("player_name", player_Name);
            argumentsOfScenario.Add("player_color", player_Color);
            argumentsOfScenario.Add("expected_position", expected_Position);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Add a new player profile", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 18
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 19
    await testRunner.GivenAsync(string.Format("I have {0} in my player list", existing_Players), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 20
    await testRunner.AndAsync("I navigate to the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 21
    await testRunner.WhenAsync("I tap the \"Add Player\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 22
    await testRunner.AndAsync(string.Format("I enter \"{0}\" as the player name", player_Name), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 23
    await testRunner.AndAsync(string.Format("I select a {0} color for the player", player_Color), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 24
    await testRunner.AndAsync("I tap \"Save Player\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 25
    await testRunner.ThenAsync(string.Format("the player \"{0}\" should be added to the player list", player_Name), ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 26
    await testRunner.AndAsync(string.Format("the player should have a {0} color indicator", player_Color), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 27
    await testRunner.AndAsync(string.Format("the player should be player number {0} in the list", expected_Position), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Edit an existing player profile")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Edit an existing player profile")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task EditAnExistingPlayerProfile()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Edit an existing player profile", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 35
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 36
    await testRunner.GivenAsync("I have a player \"Jane Smith\" with a red color", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 37
    await testRunner.AndAsync("I am on the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 38
    await testRunner.WhenAsync("I tap on the \"Jane Smith\" player", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 39
    await testRunner.AndAsync("I change the player name to \"Jane Wilson\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 40
    await testRunner.AndAsync("I change the color to green", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 41
    await testRunner.AndAsync("I tap \"Save Changes\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 42
    await testRunner.ThenAsync("the player should be updated to \"Jane Wilson\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 43
    await testRunner.AndAsync("the player should have a green color indicator", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Delete a player profile")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Delete a player profile")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task DeleteAPlayerProfile()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Delete a player profile", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 46
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 47
    await testRunner.GivenAsync("I have a player \"Bob Johnson\" in the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 48
    await testRunner.AndAsync("I am on the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 49
    await testRunner.WhenAsync("I tap on the \"Bob Johnson\" player", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 50
    await testRunner.AndAsync("I tap \"Delete Player\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 51
    await testRunner.AndAsync("I confirm the deletion", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 52
    await testRunner.ThenAsync("\"Bob Johnson\" should be removed from the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Mark a player as favorite")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Mark a player as favorite")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task MarkAPlayerAsFavorite()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Mark a player as favorite", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 55
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 56
    await testRunner.GivenAsync("I have a player \"Alice Brown\" in the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 57
    await testRunner.AndAsync("I am on the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 58
    await testRunner.WhenAsync("I tap on the \"Alice Brown\" player", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 59
    await testRunner.AndAsync("I toggle the \"Favorite\" option to enabled", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 60
    await testRunner.AndAsync("I tap \"Save Changes\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 61
    await testRunner.ThenAsync("\"Alice Brown\" should be marked as a favorite player", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 62
    await testRunner.AndAsync("\"Alice Brown\" should appear at the top of the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot add player with empty name")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Cannot add player with empty name")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotAddPlayerWithEmptyName()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot add player with empty name", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 65
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 66
    await testRunner.GivenAsync("I am on the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 67
    await testRunner.WhenAsync("I tap the \"Add Player\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 68
    await testRunner.AndAsync("I leave the player name field empty", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 69
    await testRunner.AndAsync("I tap \"Save Player\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 70
    await testRunner.ThenAsync("I should see an error message \"Player name is required\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 71
    await testRunner.AndAsync("the player should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot add duplicate player names")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Cannot add duplicate player names")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotAddDuplicatePlayerNames()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot add duplicate player names", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 74
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 75
    await testRunner.GivenAsync("I have a player \"Mike Davis\" in the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 76
    await testRunner.AndAsync("I am on the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 77
    await testRunner.WhenAsync("I tap the \"Add Player\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 78
    await testRunner.AndAsync("I enter \"Mike Davis\" as the player name", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 79
    await testRunner.AndAsync("I tap \"Save Player\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 80
    await testRunner.ThenAsync("I should see an error message \"A player with this name already exists\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 81
    await testRunner.AndAsync("the duplicate player should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Add player with maximum allowed name length")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Add player with maximum allowed name length")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        public async global::System.Threading.Tasks.Task AddPlayerWithMaximumAllowedNameLength()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Add player with maximum allowed name length", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 84
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 85
    await testRunner.GivenAsync("I am on the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 86
    await testRunner.WhenAsync("I tap the \"Add Player\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 87
    await testRunner.AndAsync("I enter a player name with 50 characters", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 88
    await testRunner.AndAsync("I tap \"Save Player\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 89
    await testRunner.ThenAsync("the player should be added successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 90
    await testRunner.AndAsync("the full name should be displayed correctly", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot add player with name exceeding maximum length")]
        [Xunit.TraitAttribute("FeatureTitle", "Player Management")]
        [Xunit.TraitAttribute("Description", "Cannot add player with name exceeding maximum length")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotAddPlayerWithNameExceedingMaximumLength()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot add player with name exceeding maximum length", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 93
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 94
    await testRunner.GivenAsync("I am on the player management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 95
    await testRunner.WhenAsync("I tap the \"Add Player\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 96
    await testRunner.AndAsync("I enter a player name with 51 characters", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 97
    await testRunner.AndAsync("I tap \"Save Player\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 98
    await testRunner.ThenAsync("I should see an error message \"Player name cannot exceed 50 characters\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 99
    await testRunner.AndAsync("the player should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await PlayerManagementFeature.FeatureSetupAsync();
            }
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await PlayerManagementFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
